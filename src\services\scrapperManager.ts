import { TelegramClient } from 'telegram';
import { StringSession } from 'telegram/sessions/index.js';
import { NewMessage } from 'telegram/events/index.js';
import { TelegramBotService } from "./telegramBotService.js";
import { SnipeStrategy } from "../strategies/snipeStrategy.js";

import { extractContractAddresses } from "../utils/messageUtils.js";
import { checkSnipePositionByToken, formatSnipePosition } from "./positionService.js";
import { CONFIG } from "../config/constants.js";
import dotenv from 'dotenv';
import * as readline from 'readline';

dotenv.config();

interface PendingAuth {
  chatId: number;
  client: TelegramClient;
  resolve: (success: boolean) => void;
  reject: (error: any) => void;
}

export class ScrapperManager {
  private snipeStrategy: SnipeStrategy;
  private botService: TelegramBotService;
  private isMonitoring: boolean = false;
  private telegramClient: TelegramClient | null = null;
  private pendingAuth: PendingAuth | null = null;
  private monitoredChannels: Set<string> = new Set();
  private scrapperEnabled: boolean = false; // Flag to control scrapper state

  constructor(botService: TelegramBotService) {
    this.botService = botService;
    this.snipeStrategy = new SnipeStrategy();

    // Auto-start scrapper on app launch - DISABLED
    // this.initializeScrapper();
  }

  // Initialize scrapper on app startup
  private async initializeScrapper(): Promise<void> {
    console.log('🚀 Initializing scrapper on startup...');
    this.scrapperEnabled = true;

    // Start scrapper automatically (no chatId needed for console mode)
    await this.startScrapperConsole();
  }



  // Console-based scrapper startup
  private async startScrapperConsole(): Promise<void> {
    try {
      console.log('🔄 Starting Telegram scrapper (console mode)...');

      // Get credentials from .env file
      const apiId = parseInt(process.env.TELEGRAM_API_ID || '0');
      const apiHash = process.env.TELEGRAM_API_HASH || '';
      const phoneNumber = process.env.TELEGRAM_PHONE || '';
      const sessionString = (process.env.TELEGRAM_SESSION_STRING || '').trim();

      if (!apiId || !apiHash || !phoneNumber) {
        console.error('❌ Missing Telegram credentials in .env file');
        console.log('Please set up TELEGRAM_API_ID, TELEGRAM_API_HASH, and TELEGRAM_PHONE');
        this.scrapperEnabled = false;
        return;
      }

      console.log(`📱 Session string length: ${sessionString.length}`);

      // Create Telegram client
      let session;
      try {
        session = new StringSession(sessionString);
      } catch (error) {
        console.log('⚠️ Invalid session string, using empty session');
        session = new StringSession('');
      }

      this.telegramClient = new TelegramClient(session, apiId, apiHash, {
        connectionRetries: 5,
      });

      // Start authentication
      const maskedPhone = phoneNumber.replace(/(\+\d{1,3})(\d{3})(\d{3})(\d{4})/, '$1$2•••$4');
      console.log(`🔗 Connecting to Telegram for phone: ${maskedPhone}`);

      // Try authentication
      const authSuccess = await this.authenticateWithTelegramConsole(phoneNumber);

      if (authSuccess) {
        // Start monitoring (no chatId needed for console mode)
        await this.startChannelMonitoring(0); // Use 0 as dummy chatId for console mode
        this.isMonitoring = true;
        console.log('✅ Scrapper started successfully and monitoring @stonks_gem_bot');
      } else {
        console.log('⚠️ Authentication failed. Scrapper disabled.');
        this.scrapperEnabled = false;
      }

    } catch (error) {
      console.error('❌ Failed to start scrapper:', error);
      this.scrapperEnabled = false;
    }
  }

  // Console-based authentication that waits for user input
  private async authenticateWithTelegramConsole(phoneNumber: string): Promise<boolean> {
    try {
      console.log('🔄 Starting Telegram authentication...');

      // Use the simpler start() method which handles SMS automatically
      await this.telegramClient!.start({
        phoneNumber: async () => {
          console.log(`📱 Using phone number: ${phoneNumber}`);
          return phoneNumber;
        },
        password: async () => {
          console.log('🔐 2FA password required.');
          return await this.getConsoleInput('Enter your 2FA password: ');
        },
        phoneCode: async () => {
          const maskedPhone = phoneNumber.replace(/(\+\d{1,3})(\d{3})(\d{3})(\d{4})/, '$1$2•••$4');
          console.log(`📱 Verification Code Required`);
          console.log(`Telegram should send an SMS code to: ${maskedPhone}`);
          console.log('');

          return await this.getConsoleInput('Enter verification code: ');
        },
        onError: (err) => {
          console.error('❌ Telegram connection error:', err.message);
        },
      });

      console.log('✅ Authentication completed successfully');

      // Save session
      const sessionString = this.telegramClient!.session.save();
      console.log('💾 Session saved for future use');

      return true;

    } catch (error) {
      console.error('❌ Authentication failed:', error);

      const errorMessage = error instanceof Error ? error.message : String(error);

      if (errorMessage.includes('PHONE_CODE_INVALID')) {
        console.log('❌ Invalid verification code. Please restart the app and try again.');
      } else if (errorMessage.includes('FLOOD') || errorMessage.includes('wait') || errorMessage.includes('seconds is required')) {
        const waitMatch = errorMessage.match(/(\d+) seconds is required/);
        const waitHours = waitMatch ? Math.round(parseInt(waitMatch[1]) / 3600) : 21;
        console.log(`🕐 Telegram Rate Limit: Please wait ~${waitHours} hours and try again.`);
      } else {
        console.log(`❌ Error: ${errorMessage}`);
      }

      return false;
    }
  }

  // Helper method to get console input
  private async getConsoleInput(prompt: string): Promise<string> {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise((resolve) => {
      rl.question(prompt, (answer) => {
        rl.close();
        resolve(answer.trim());
      });
    });
  }

  // Enable/disable scrapper
  setEnabled(enabled: boolean): void {
    this.scrapperEnabled = enabled;
    console.log(`🔄 Scrapper ${enabled ? 'enabled' : 'disabled'}`);
  }

  async startScrapper(chatId: number): Promise<boolean> {
    try {
      console.log('🔄 Starting Telegram scrapper...');

      // Enable scrapper flag
      this.scrapperEnabled = true;

      // Get credentials from .env file
      const apiId = parseInt(process.env.TELEGRAM_API_ID || '0');
      const apiHash = process.env.TELEGRAM_API_HASH || '';
      const phoneNumber = process.env.TELEGRAM_PHONE || '';
      const sessionString = (process.env.TELEGRAM_SESSION_STRING || '').trim();

      if (!apiId || !apiHash || !phoneNumber) {
        console.error('❌ Missing Telegram credentials');
        await this.botService.sendMessage(
          chatId,
          "❌ Telegram credentials not configured. Please set up TELEGRAM_API_ID, TELEGRAM_API_HASH, and TELEGRAM_PHONE in .env file."
        );
        this.scrapperEnabled = false;
        return false;
      }

      console.log(`📱 Session string length: ${sessionString.length}`);

      // Create Telegram client with proper session handling
      let session;
      try {
        session = new StringSession(sessionString);
      } catch (error) {
        console.log('⚠️ Invalid session string, using empty session');
        session = new StringSession('');
      }
      this.telegramClient = new TelegramClient(session, apiId, apiHash, {
        connectionRetries: 5,
      });

      // Start authentication process
      const maskedPhone = phoneNumber.replace(/(\+\d{1,3})(\d{3})(\d{3})(\d{4})/, '$1$2•••$4');
      await this.botService.sendMessage(
        chatId,
        `🔗 Connecting to Telegram for phone: ${maskedPhone}\n\n⏳ Please wait...`
      );

      // Try authentication - but don't fail the entire app if it fails
      try {
        const authSuccess = await this.authenticateWithTelegram(chatId, phoneNumber);

        if (authSuccess) {
          // Start monitoring
          await this.startChannelMonitoring(chatId);
          this.isMonitoring = true;
          console.log('✅ Scrapper started successfully');
          return true;
        } else {
          await this.botService.sendMessage(
            chatId,
            "⚠️ Authentication failed, but bot is still running.\n\n💡 Enter verification code when you receive SMS, or try /scrapper again later."
          );
          return false;
        }
      } catch (authError) {
        console.error('❌ Authentication error:', authError);
        await this.botService.sendMessage(
          chatId,
          "⚠️ Authentication failed, but bot is still running.\n\n💡 Enter verification code when you receive SMS, or try /scrapper again later."
        );
        return false;
      }

    } catch (error) {
      console.error('❌ Failed to start scrapper:', error);

      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.botService.sendMessage(
        chatId,
        `⚠️ Scrapper startup failed, but bot is still running.\n\n💡 Error: ${errorMessage}\n\nTry /scrapper again later.`
      );

      this.scrapperEnabled = false;
      return false;
    }
  }

  private async authenticateWithTelegram(chatId: number, phoneNumber: string): Promise<boolean> {
    // Set up pending auth FIRST and keep it until user provides code


  this.pendingAuth = {
      chatId,
      client: this.telegramClient!,
      resolve: () => {},
      reject: () => {}
    };
    console.log(`🔄 Set pendingAuth for chat ${chatId}`);

    try {
      console.log('🔄 Trying simplified authentication approach...');

      // Use the simpler start() method which handles SMS automatically
      await this.telegramClient!.start({
        phoneNumber: async () => {
          console.log(`📱 Using phone number: ${phoneNumber}`);
          return phoneNumber;
        },
        password: async () => {
          await this.botService.sendMessage(
            chatId,
            "🔐 2FA password required. Please reply with your 2FA password:"
          );

          return new Promise((resolve) => {
            (this.pendingAuth as any).passwordResolve = resolve;
          });
        },
        phoneCode: async () => {
          const maskedPhone = phoneNumber.replace(/(\+\d{1,3})(\d{3})(\d{3})(\d{4})/, '$1$2•••$4');
          await this.botService.sendMessage(
            chatId,
            `📱 Verification Code Required\n\nTelegram should send an SMS code to: ${maskedPhone}\n\n💬 Please reply with the verification code when you receive it.\n\n⚠️ If you don't receive SMS, check if the phone number ${phoneNumber} is correct.`
          );

          console.log('📱 Waiting for verification code from user...');

          return new Promise((resolve, reject) => {
            (this.pendingAuth as any).codeResolve = resolve;

            // Add timeout for code input - but DON'T clear pendingAuth on timeout
            setTimeout(() => {
              console.log('⏰ Verification code input timeout (5 minutes)');
              reject(new Error('Verification code timeout'));
            }, 300000); // 5 minutes timeout
          });
        },
        onError: (err) => {
          console.error('❌ Telegram connection error:', err.message);
          // DON'T clear pendingAuth here - let user try again
        },
      });

      console.log('✅ Authentication completed successfully');

      // Save session
      const sessionString = this.telegramClient!.session.save();
      console.log('💾 Session string for future use:', sessionString);

      await this.botService.sendMessage(
        chatId,
        `✅ Authentication Successful!\n\n💾 Session saved. Future connections won't require SMS codes.\n\n📡 Starting channel monitoring...`
      );

      console.log('🔄 Clearing pendingAuth - Authentication successful');
      this.pendingAuth = null;
      return true;

    } catch (error) {
      console.error('❌ Authentication failed:', error);

      const errorMessage = error instanceof Error ? error.message : String(error);

      // DON'T clear pendingAuth immediately - let user try verification code
      if (errorMessage.includes('PHONE_CODE_INVALID')) {
        await this.botService.sendMessage(chatId, "❌ Invalid verification code. Please try again with a new code.");
        // Keep pendingAuth for retry
        return false;
      } else if (errorMessage.includes('timeout')) {
        await this.botService.sendMessage(chatId, "⏰ Waiting for verification code. Please enter it when you receive the SMS.");
        // Keep pendingAuth for user input
        return false;
      } else {
        // Only clear pendingAuth for serious errors
        let userMessage = "❌ Authentication Failed\n\n";

        if (errorMessage.includes('PHONE_NUMBER_INVALID')) {
          userMessage += `Phone number ${phoneNumber} is invalid. Please check the format.`;
        } else if (errorMessage.includes('FLOOD') || errorMessage.includes('wait') || errorMessage.includes('seconds is required')) {
          const waitMatch = errorMessage.match(/(\d+) seconds is required/);
          const waitHours = waitMatch ? Math.round(parseInt(waitMatch[1]) / 3600) : 21;
          userMessage += `🕐 Telegram Rate Limit\n\nToo many SMS requests have been made for your phone number.\n\n⏰ Please wait ~${waitHours} hours and try again.\n\n💡 This is a Telegram security feature to prevent spam.`;
        } else {
          userMessage += `Error: ${errorMessage}`;
        }

        await this.botService.sendMessage(chatId, userMessage);
        console.log('🔄 Clearing pendingAuth - Serious error occurred');
        this.pendingAuth = null;
        return false;
      }
    }
  }

  private async startChannelMonitoring(chatId: number): Promise<void> {
    if (!this.telegramClient) {
      throw new Error('Telegram client not initialized');
    }

    try {
      // Get channel entity
      const entity = await this.telegramClient.getEntity('@stonks_gem_bot');
      this.monitoredChannels.add(entity.id.toString());

      // Set up message listener
      this.telegramClient.addEventHandler(async (event) => {
        const message = event.message;
        if (!message || !message.peerId) return;

        const channelId = (message.peerId as any).channelId?.toString();
        if (!channelId || !this.monitoredChannels.has(channelId)) return;

        await this.handleNewMessage(message);
      }, new NewMessage({}));

      console.log('✅ Started monitoring @stonks_gem_bot');

    } catch (error) {
      console.error('❌ Error starting channel monitoring:', error);
      throw error;
    }
  }

  async stopScrapper(chatId: number): Promise<void> {
    try {
      // Disable scrapper flag
      this.scrapperEnabled = false;

      if (this.telegramClient) {
        // Clear monitored channels
        this.monitoredChannels.clear();
        // Note: We don't disconnect the client as it might be used elsewhere
      }

      this.isMonitoring = false;

      console.log('✅ Scrapper stopped successfully');

    } catch (error) {
      console.error('❌ Error stopping scrapper:', error);
      this.scrapperEnabled = false; // Ensure it's disabled even on error
      await this.botService.sendMessage(
        chatId,
        "❌ Error stopping scrapper, but it has been deactivated."
      );
    }
  }

  // Simple method to use any verification code
  async useVerificationCode(code: string, chatId: number): Promise<boolean> {
    console.log(`📱 Received verification code via Telegram: ${code}`);

    // In console mode, verification codes should be entered in console, not Telegram
    await this.botService.sendMessage(
      chatId,
      `💡 Console Mode Active\n\nVerification codes should be entered in the console where the app is running, not here in Telegram.\n\nCheck your console/terminal for the verification code prompt.`
    );
    return false;
  }



  // Handle verification code from any chat (for cross-chat authentication)
  async handleVerificationCodeAny(code: string, fromChatId: number): Promise<boolean> {
    console.log(`📱 Received verification code from any chat: ${code} from chat: ${fromChatId}`);

    if (!this.pendingAuth) {
      console.log('❌ No pending auth available');
      return false;
    }

    // Use the pending auth regardless of chat ID
    const originalChatId = this.pendingAuth.chatId;
    console.log(`🔄 Using pending auth from chat ${originalChatId} for code from chat ${fromChatId}`);

    if ((this.pendingAuth as any).codeResolve) {
      console.log('✅ Resolving verification code promise (cross-chat)');
      (this.pendingAuth as any).codeResolve(code);

      // Notify both chats
      await this.botService.sendMessage(
        originalChatId,
        `✅ Verification code received from another chat and processed!`
      );

      if (fromChatId !== originalChatId) {
        await this.botService.sendMessage(
          fromChatId,
          `✅ Your verification code was used for authentication in another chat.`
        );
      }

      return true;
    }

    console.log('❌ No code resolve function available');
    return false;
  }



  // Method to handle 2FA password input from user
  async handle2FAPassword(password: string, chatId: number): Promise<boolean> {
    if (!this.pendingAuth || this.pendingAuth.chatId !== chatId) {
      return false;
    }

    if ((this.pendingAuth as any).passwordResolve) {
      (this.pendingAuth as any).passwordResolve(password);
      return true;
    }

    return false;
  }

  // Check if waiting for auth input
  isWaitingForAuth(chatId: number): boolean {
    const isWaiting = this.pendingAuth !== null && this.pendingAuth.chatId === chatId;
    console.log(`🔍 Auth status check - pendingAuth: ${this.pendingAuth !== null}, chatId match: ${this.pendingAuth?.chatId === chatId}, result: ${isWaiting}`);
    return isWaiting;
  }

  // Check if ANY authentication is pending (for cross-chat verification codes)
  isAnyAuthPending(): boolean {
    return this.pendingAuth !== null;
  }

  // Cancel pending authentication
  cancelAuthentication(chatId: number): boolean {
    if (this.pendingAuth && this.pendingAuth.chatId === chatId) {
      console.log('🛑 Cancelling authentication...');
      this.pendingAuth.reject(new Error('Authentication cancelled by user'));
      this.pendingAuth = null;
      return true;
    }
    return false;
  }

  private async sendAutoSnipeCompletionWithPosition(contractAddress: string, amount: string): Promise<void> {
    try {
      const chatId = 1382432100; // Default chat ID for auto-snipe notifications

      // Send initial completion message
      await this.botService.sendMessage(
        chatId,
        `✅ Auto-snipe completed!\n\n💰 Amount: ${amount} TON\n🎯 Contract: ${contractAddress}\n\n🔍 Checking your position in the buying queue...`
      );

      const myAddress = CONFIG.WALLET_ADDRESS;

      // Check position
      const position = await checkSnipePositionByToken(contractAddress, myAddress);

      if (position) {
        const positionMessage = formatSnipePosition(position);
        await this.botService.sendMessage(
          chatId,
          `🤖 Auto-Snipe Results:\n\n${positionMessage}`
        );
      } else {
        await this.botService.sendMessage(
          chatId,
          `✅ Auto-snipe completed for ${amount} TON\n\n⚠️ Could not determine position in buying queue.\nThis might be normal if liquidity was just added or transactions are still processing.`
        );
      }

    } catch (error) {
      console.error('Error checking auto-snipe position:', error);
      // Still send completion message even if position check fails
      await this.botService.sendMessage(
        1382432100,
        `✅ Auto-snipe completed for ${amount} TON\n\n❌ Position check failed, but your snipe was successful!`
      );
    }
  }

  private async handleNewMessage(message: any): Promise<void> {
    try {
      console.log(`📨 New message from @stonks_gem_bot: ${message.text.substring(0, 100)}...`);

      // Extract contract addresses from the message
      const contractAddresses = extractContractAddresses(message.text);
      
      if (contractAddresses && contractAddresses.length > 0) {
        const contractAddress = contractAddresses[0];
        
        console.log(`🎯 Found contract address: ${contractAddress}`);
        
        // Notify user about the detected contract
        await this.botService.sendMessage(
          1382432100,
          `🤖 Scrapper Alert!\n\n` +
          `📡 New message detected in @stonks_gem_bot\n` +
          `🎯 Contract found: ${contractAddress}\n` +
          `🚀 Starting auto-snipe for 2 TON...`
        );

        // Start sniping with 2 TON
        try {
          await this.snipeStrategy.executeSimpleSnipe(contractAddress, "2");

          // Send completion message with position check
          await this.sendAutoSnipeCompletionWithPosition(contractAddress, "2");

        } catch (snipeError) {
          console.error('❌ Auto-snipe failed:', snipeError);

          await this.botService.sendMessage(
            1382432100,
            `❌ Auto-snipe failed!\n\n` +
            `🎯 Contract: ${contractAddress}\n` +
            `💥 Error: ${snipeError instanceof Error ? snipeError.message : String(snipeError)}`
          );
        }
      } else {
        console.log('📝 No contract addresses found in message');
      }

    } catch (error) {
      console.error('❌ Error handling new message:', error);
    }
  }

  isActive(): boolean {
    return this.scrapperEnabled && this.isMonitoring;
  }
}
